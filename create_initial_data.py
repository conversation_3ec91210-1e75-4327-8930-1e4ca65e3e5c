"""
سكريبت لإنشاء البيانات الأولية للنظام
"""
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuel_inventory.settings')
django.setup()

from django.contrib.auth.models import User
from inventory.models import Store, Item, Station, Supplier, Beneficiary, Inventory


def create_initial_data():
    """إنشاء البيانات الأولية"""
    
    print("إنشاء البيانات الأولية...")
    
    # إنشاء مستخدم المدير
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='مدير',
            last_name='النظام'
        )
        admin_user.userprofile.user_type = 'admin'
        admin_user.userprofile.full_name = 'مدير النظام'
        admin_user.userprofile.save()
        print("✓ تم إنشاء مستخدم المدير")
    
    # إنشاء مخازن تجريبية
    stores_data = [
        {'store_name': 'المخزن الرئيسي', 'classification': 'رئيسي', 'storekeeper': 'أحمد محمد', 'phone': '0501234567'},
        {'store_name': 'مخزن الطوارئ', 'classification': 'فرعي', 'storekeeper': 'محمد أحمد', 'phone': '0507654321'},
        {'store_name': 'مخزن الصيانة', 'classification': 'صيانة', 'storekeeper': 'علي سالم', 'phone': '0509876543'},
    ]
    
    for store_data in stores_data:
        store, created = Store.objects.get_or_create(
            store_name=store_data['store_name'],
            defaults=store_data
        )
        if created:
            print(f"✓ تم إنشاء المخزن: {store.store_name}")
    
    # إنشاء أصناف تجريبية
    items_data = [
        {'item_name': 'بنزين 91', 'unit_of_measure': 'لتر', 'minimum_stock': 1000},
        {'item_name': 'بنزين 95', 'unit_of_measure': 'لتر', 'minimum_stock': 500},
        {'item_name': 'ديزل', 'unit_of_measure': 'لتر', 'minimum_stock': 2000},
        {'item_name': 'زيت محرك', 'unit_of_measure': 'لتر', 'minimum_stock': 100},
        {'item_name': 'فلتر هواء', 'unit_of_measure': 'قطعة', 'minimum_stock': 50},
    ]
    
    for item_data in items_data:
        item, created = Item.objects.get_or_create(
            item_name=item_data['item_name'],
            defaults=item_data
        )
        if created:
            print(f"✓ تم إنشاء الصنف: {item.item_name}")
    
    # إنشاء محطات تجريبية
    stations_data = [
        {'station_name': 'محطة الشرق', 'address': 'شارع الملك فهد، الرياض', 'contact_person': 'سالم العتيبي'},
        {'station_name': 'محطة الغرب', 'address': 'شارع الأمير سلطان، الرياض', 'contact_person': 'فهد المطيري'},
        {'station_name': 'محطة الشمال', 'address': 'شارع العليا، الرياض', 'contact_person': 'خالد الدوسري'},
    ]
    
    for station_data in stations_data:
        station, created = Station.objects.get_or_create(
            station_name=station_data['station_name'],
            defaults=station_data
        )
        if created:
            print(f"✓ تم إنشاء المحطة: {station.station_name}")
    
    # إنشاء موردين تجريبيين
    suppliers_data = [
        {'full_name': 'شركة البترول السعودية', 'phone': '0112345678', 'company_name': 'أرامكو'},
        {'full_name': 'شركة المحروقات المتقدمة', 'phone': '0118765432', 'company_name': 'شركة المحروقات'},
        {'full_name': 'مؤسسة الوقود الحديث', 'phone': '0119876543', 'company_name': 'الوقود الحديث'},
    ]
    
    for supplier_data in suppliers_data:
        supplier, created = Supplier.objects.get_or_create(
            full_name=supplier_data['full_name'],
            defaults=supplier_data
        )
        if created:
            print(f"✓ تم إنشاء المورد: {supplier.full_name}")
    
    # إنشاء مستفيدين تجريبيين
    beneficiaries_data = [
        {'full_name': 'وزارة الداخلية', 'phone': '0114567890', 'organization': 'وزارة الداخلية'},
        {'full_name': 'وزارة الدفاع', 'phone': '0116789012', 'organization': 'وزارة الدفاع'},
        {'full_name': 'الحرس الوطني', 'phone': '0118901234', 'organization': 'الحرس الوطني'},
    ]
    
    for beneficiary_data in beneficiaries_data:
        beneficiary, created = Beneficiary.objects.get_or_create(
            full_name=beneficiary_data['full_name'],
            defaults=beneficiary_data
        )
        if created:
            print(f"✓ تم إنشاء المستفيد: {beneficiary.full_name}")
    
    # إنشاء مخزون أولي
    stores = Store.objects.all()
    items = Item.objects.all()
    
    for store in stores:
        for item in items:
            inventory, created = Inventory.objects.get_or_create(
                store=store,
                item=item,
                defaults={
                    'opening_balance': 0,
                    'current_quantity': 0
                }
            )
            if created:
                print(f"✓ تم إنشاء مخزون: {store.store_name} - {item.item_name}")
    
    print("\n✅ تم إنشاء جميع البيانات الأولية بنجاح!")


if __name__ == '__main__':
    create_initial_data()
