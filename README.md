# Website Downloader - Seha.sa

This Node.js script uses <PERSON><PERSON><PERSON><PERSON> to download the complete website design from https://seha.sa/#/inquiries/slenquiry

## Features

- ✅ Downloads fully rendered HTML (after JavaScript execution)
- ✅ Captures all CSS, JavaScript, images, and font files
- ✅ Takes a full-page screenshot
- ✅ Generates a PDF of the page
- ✅ Organizes files in a clean folder structure

## Prerequisites

- Node.js (version 14 or higher)
- npm (comes with Node.js)

## Installation & Usage

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Run the Downloader
```bash
npm start
```

Or directly:
```bash
node download-website.js
```

## What Gets Downloaded

The script will create a `downloaded-website` folder containing:

```
downloaded-website/
├── index.html          # Main HTML file (fully rendered)
├── screenshot.png      # Full-page screenshot
├── page.pdf           # PDF version of the page
└── assets/            # All website resources
    ├── styles.css     # CSS files
    ├── scripts.js     # JavaScript files
    ├── images.png     # Image files
    └── fonts.woff     # Font files
```

## Configuration Options

You can modify the script to:

- **Change headless mode**: Set `headless: true` in the browser launch options to run without GUI
- **Adjust viewport**: Modify the `defaultViewport` settings
- **Change timeout**: Adjust the `timeout` values for slower connections
- **Add more wait conditions**: Include additional `waitForSelector` calls for specific elements

## Troubleshooting

### Common Issues:

1. **"Module not found" error**
   ```bash
   npm install
   ```

2. **Browser launch fails**
   - Make sure you have sufficient permissions
   - Try running with `headless: true`

3. **Timeout errors**
   - Increase timeout values in the script
   - Check your internet connection

4. **Missing resources**
   - Some resources might be blocked by CORS
   - The script captures what's accessible

## Legal Notice

⚠️ **Important**: This tool is for educational purposes. Please:
- Respect the website's Terms of Service
- Check robots.txt before downloading
- Consider copyright and intellectual property rights
- Use responsibly and ethically

## Script Behavior

1. Launches a Chrome browser instance
2. Navigates to the target URL
3. Waits for all network activity to complete
4. Captures all resources (CSS, JS, images, fonts)
5. Saves the fully rendered HTML
6. Takes a screenshot and generates PDF
7. Organizes everything in the output folder

## Output Information

The script provides real-time feedback:
- 🚀 Starting download
- 📱 Browser launch status
- 🌐 Navigation progress
- 📥 Resource capture status
- 💾 File saving progress
- ✅ Completion summary

Enjoy your downloaded website design! 🎉
