#!/usr/bin/env python
"""
Script to create superuser
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'warehouse_system.settings')
django.setup()

from warehouse.models import User

def create_superuser():
    """Create superuser if not exists"""
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            password='admin123',
            full_name='مدير النظام',
            user_type='admin',
            email='<EMAIL>'
        )
        print("Superuser created successfully!")
        print("Username: admin")
        print("Password: admin123")
    else:
        print("Superuser already exists!")

if __name__ == '__main__':
    create_superuser()
