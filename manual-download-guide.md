# Manual Website Download Guide for Seha.sa

Since the website is returning "503 Service unavailable" for automated requests, here are manual methods to download the website design:

## Method 1: Browser Developer Tools (Recommended)

### Step 1: Open the Website
1. Open your browser and go to: https://seha.sa/#/inquiries/slenquiry
2. Wait for the page to fully load

### Step 2: Save Complete Page
1. Right-click on the page
2. Select "Save as..." or "Save page as..."
3. Choose "Webpage, Complete" or "Web Page, Complete"
4. This will save the HTML and create a folder with all assets

### Step 3: Use Developer Tools for Assets
1. Press F12 to open Developer Tools
2. Go to the "Network" tab
3. Refresh the page (Ctrl+F5)
4. You'll see all resources loading
5. Filter by:
   - **CSS**: Right-click → Save as
   - **JS**: Right-click → Save as  
   - **Images**: Right-click → Save as
   - **Fonts**: Right-click → Save as

## Method 2: Browser Extensions

### SingleFile Extension
1. Install "SingleFile" extension for Chrome/Firefox
2. Visit the website
3. Click the SingleFile icon
4. It will save the entire page as one HTML file with embedded CSS/JS

### Save Page WE Extension
1. Install "Save Page WE" extension
2. Visit the website
3. Click the extension icon
4. Choose your save options
5. Download complete page with assets

## Method 3: Screenshot and PDF

### Full Page Screenshot
1. Press F12 → Console tab
2. Type: `document.body.style.zoom = "0.5"` (to fit more content)
3. Use browser's screenshot tool or extension like "Full Page Screen Capture"

### Save as PDF
1. Press Ctrl+P (Print)
2. Choose "Save as PDF"
3. Select "More settings" → "Paper size" → A4
4. Check "Background graphics"
5. Save

## Method 4: Manual Inspection

### Extract CSS Styles
1. F12 → Elements tab
2. Right-click on `<html>` → Copy → Copy outerHTML
3. Save to a .html file
4. In Styles panel, copy CSS rules you need

### Extract Design Elements
1. Right-click on images → "Save image as"
2. Check computed styles for colors, fonts, spacing
3. Note layout structure and component hierarchy

## Method 5: Use Our Downloaded Assets

We already successfully downloaded:
- ✅ CSS frameworks (Bootstrap, Ant Design)
- ✅ JavaScript components
- ✅ All logos and SVG files
- ✅ Web fonts
- ✅ Third-party scripts

You can use these assets as a foundation and manually recreate the HTML structure.

## Why 503 Error Occurred

The website likely has:
- **Rate limiting**: Too many requests from same IP
- **Bot detection**: Automated request blocking
- **Geographic restrictions**: May block certain regions
- **Security measures**: Government/healthcare sites often have strict access controls

## Next Steps

1. **Try manual browser methods first** - these are most likely to work
2. **Use a VPN** if you suspect geographic blocking
3. **Wait and retry** - the 503 might be temporary
4. **Use the assets we downloaded** to recreate the design

## Legal Reminder

- ✅ Viewing and saving for personal/educational use is generally acceptable
- ✅ Studying design patterns and layouts for learning
- ❌ Don't republish or use commercially without permission
- ❌ Respect the website's terms of service

The manual browser methods are your best bet for getting the complete page content!
