const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const path = require('path');

async function downloadWebsite() {
    console.log('🚀 Starting website download...');
    
    // Create output directory
    const outputDir = './downloaded-website';
    await fs.ensureDir(outputDir);
    await fs.ensureDir(path.join(outputDir, 'assets'));
    
    let browser;
    
    try {
        // Launch browser
        console.log('📱 Launching browser...');
        browser = await puppeteer.launch({
            headless: false, // Set to true if you don't want to see the browser
            defaultViewport: { width: 1920, height: 1080 }
        });
        
        const page = await browser.newPage();
        
        // Set user agent to avoid blocking
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // Enable request interception to capture resources
        await page.setRequestInterception(true);
        
        const resources = new Map();
        
        page.on('request', (request) => {
            request.continue();
        });
        
        page.on('response', async (response) => {
            const url = response.url();
            const resourceType = response.request().resourceType();
            
            // Capture CSS, JS, images, and fonts
            if (['stylesheet', 'script', 'image', 'font'].includes(resourceType)) {
                try {
                    const buffer = await response.buffer();
                    resources.set(url, {
                        buffer: buffer,
                        type: resourceType,
                        contentType: response.headers()['content-type'] || ''
                    });
                    console.log(`📥 Captured: ${resourceType} - ${url}`);
                } catch (error) {
                    console.log(`❌ Failed to capture: ${url} - ${error.message}`);
                }
            }
        });
        
        // Navigate to the website
        console.log('🌐 Navigating to website...');
        await page.goto('https://seha.sa/#/inquiries/slenquiry', {
            waitUntil: 'networkidle0',
            timeout: 60000
        });
        
        // Wait for additional content to load
        console.log('⏳ Waiting for content to load...');
        await page.waitForTimeout(5000);
        
        // Try to wait for specific elements (adjust selectors as needed)
        try {
            await page.waitForSelector('body', { timeout: 10000 });
        } catch (error) {
            console.log('⚠️ Timeout waiting for specific elements, continuing...');
        }
        
        // Get the full HTML after JavaScript execution
        console.log('📄 Extracting HTML content...');
        const html = await page.content();
        
        // Save the main HTML file
        await fs.writeFile(path.join(outputDir, 'index.html'), html);
        console.log('✅ Saved main HTML file');
        
        // Save all captured resources
        console.log('💾 Saving captured resources...');
        let savedCount = 0;
        
        for (const [url, resource] of resources) {
            try {
                // Create a safe filename from URL
                const urlObj = new URL(url);
                let filename = path.basename(urlObj.pathname) || 'resource';
                
                // Add extension based on content type if missing
                if (!path.extname(filename)) {
                    if (resource.contentType.includes('css')) filename += '.css';
                    else if (resource.contentType.includes('javascript')) filename += '.js';
                    else if (resource.contentType.includes('image/png')) filename += '.png';
                    else if (resource.contentType.includes('image/jpeg')) filename += '.jpg';
                    else if (resource.contentType.includes('image/svg')) filename += '.svg';
                    else if (resource.contentType.includes('font')) filename += '.woff';
                }
                
                // Ensure unique filename
                let counter = 1;
                let finalFilename = filename;
                while (await fs.pathExists(path.join(outputDir, 'assets', finalFilename))) {
                    const ext = path.extname(filename);
                    const name = path.basename(filename, ext);
                    finalFilename = `${name}_${counter}${ext}`;
                    counter++;
                }
                
                await fs.writeFile(path.join(outputDir, 'assets', finalFilename), resource.buffer);
                savedCount++;
                
            } catch (error) {
                console.log(`❌ Failed to save resource ${url}: ${error.message}`);
            }
        }
        
        console.log(`✅ Saved ${savedCount} resources`);
        
        // Take a screenshot
        console.log('📸 Taking screenshot...');
        await page.screenshot({
            path: path.join(outputDir, 'screenshot.png'),
            fullPage: true
        });
        
        // Save page as PDF
        console.log('📄 Generating PDF...');
        await page.pdf({
            path: path.join(outputDir, 'page.pdf'),
            format: 'A4',
            printBackground: true
        });
        
        console.log('🎉 Website download completed successfully!');
        console.log(`📁 Files saved to: ${path.resolve(outputDir)}`);
        console.log(`📊 Total files: ${savedCount + 3} (HTML + Screenshot + PDF + ${savedCount} assets)`);
        
    } catch (error) {
        console.error('❌ Error downloading website:', error);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Run the download
downloadWebsite().catch(console.error);
