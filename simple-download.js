const https = require('https');
const fs = require('fs-extra');
const path = require('path');

async function downloadWebsite() {
    console.log('🚀 Starting simple website download...');
    
    // Create output directory
    const outputDir = './downloaded-website';
    await fs.ensureDir(outputDir);
    
    try {
        // Download the main HTML
        console.log('🌐 Downloading HTML content...');
        
        const html = await downloadPage('https://seha.sa/');
        
        // Save the HTML file
        await fs.writeFile(path.join(outputDir, 'index.html'), html);
        console.log('✅ Saved main HTML file');
        
        // Try to extract and download CSS and JS files
        console.log('🔍 Looking for CSS and JS files...');
        
        const cssMatches = html.match(/href=["']([^"']*\.css[^"']*)["']/g) || [];
        const jsMatches = html.match(/src=["']([^"']*\.js[^"']*)["']/g) || [];
        
        console.log(`Found ${cssMatches.length} CSS files and ${jsMatches.length} JS files`);
        
        // Download CSS files
        for (const match of cssMatches) {
            const url = match.match(/href=["']([^"']*)["']/)[1];
            await downloadResource(url, outputDir, 'css');
        }
        
        // Download JS files
        for (const match of jsMatches) {
            const url = match.match(/src=["']([^"']*)["']/)[1];
            await downloadResource(url, outputDir, 'js');
        }
        
        console.log('🎉 Download completed!');
        console.log(`📁 Files saved to: ${path.resolve(outputDir)}`);
        
    } catch (error) {
        console.error('❌ Error downloading website:', error.message);
    }
}

function downloadPage(url) {
    return new Promise((resolve, reject) => {
        const options = {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        };
        
        https.get(url, options, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                resolve(data);
            });
            
        }).on('error', (error) => {
            reject(error);
        });
    });
}

async function downloadResource(url, outputDir, type) {
    try {
        // Make URL absolute if it's relative
        if (url.startsWith('//')) {
            url = 'https:' + url;
        } else if (url.startsWith('/')) {
            url = 'https://seha.sa' + url;
        } else if (!url.startsWith('http')) {
            url = 'https://seha.sa/' + url;
        }
        
        console.log(`📥 Downloading ${type}: ${url}`);
        
        const content = await downloadPage(url);
        
        // Create filename from URL
        const urlObj = new URL(url);
        let filename = path.basename(urlObj.pathname) || `resource.${type}`;
        
        if (!filename.includes('.')) {
            filename += `.${type}`;
        }
        
        // Save to assets folder
        const assetsDir = path.join(outputDir, 'assets');
        await fs.ensureDir(assetsDir);
        await fs.writeFile(path.join(assetsDir, filename), content);
        
        console.log(`✅ Saved: ${filename}`);
        
    } catch (error) {
        console.log(`❌ Failed to download ${url}: ${error.message}`);
    }
}

// Run the download
downloadWebsite().catch(console.error);
