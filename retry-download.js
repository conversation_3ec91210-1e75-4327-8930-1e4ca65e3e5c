const https = require('https');
const fs = require('fs-extra');
const path = require('path');

async function retryDownload() {
    console.log('🔄 Retrying website download with different approach...');
    
    const outputDir = './downloaded-website-retry';
    await fs.ensureDir(outputDir);
    
    // Try different approaches
    const approaches = [
        {
            name: 'Direct page request',
            url: 'https://seha.sa/#/inquiries/slenquiry',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none'
            }
        },
        {
            name: 'Main domain request',
            url: 'https://seha.sa/',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate, br'
            }
        },
        {
            name: 'Mobile user agent',
            url: 'https://seha.sa/',
            headers: {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'ar-SA,ar;q=0.9,en;q=0.8'
            }
        }
    ];
    
    for (const approach of approaches) {
        try {
            console.log(`\n🔍 Trying: ${approach.name}`);
            console.log(`📡 URL: ${approach.url}`);
            
            const html = await downloadWithHeaders(approach.url, approach.headers);
            
            if (html && !html.includes('503 Service unavailable') && html.length > 1000) {
                console.log(`✅ Success with ${approach.name}!`);
                console.log(`📄 Content length: ${html.length} characters`);
                
                // Save the HTML
                const filename = `index-${approach.name.replace(/\s+/g, '-').toLowerCase()}.html`;
                await fs.writeFile(path.join(outputDir, filename), html);
                
                // Try to extract and download resources
                await extractResources(html, outputDir, approach.url);
                
                console.log(`✅ Saved to: ${filename}`);
                return;
                
            } else {
                console.log(`❌ ${approach.name} failed or returned error page`);
                if (html) {
                    console.log(`📄 Response preview: ${html.substring(0, 200)}...`);
                }
            }
            
            // Wait between attempts
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.log(`❌ ${approach.name} error: ${error.message}`);
        }
    }
    
    console.log('\n❌ All approaches failed. The website is likely blocking automated access.');
    console.log('📋 Please try the manual methods in manual-download-guide.md');
}

function downloadWithHeaders(url, headers) {
    return new Promise((resolve, reject) => {
        const options = {
            headers: headers,
            timeout: 30000
        };
        
        https.get(url, options, (response) => {
            let data = '';
            
            // Handle redirects
            if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
                console.log(`🔄 Redirecting to: ${response.headers.location}`);
                return downloadWithHeaders(response.headers.location, headers)
                    .then(resolve)
                    .catch(reject);
            }
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                console.log(`📊 Status: ${response.statusCode}`);
                console.log(`📏 Content-Length: ${data.length}`);
                resolve(data);
            });
            
        }).on('error', (error) => {
            reject(error);
        }).on('timeout', () => {
            reject(new Error('Request timeout'));
        });
    });
}

async function extractResources(html, outputDir, baseUrl) {
    console.log('🔍 Extracting resources from HTML...');
    
    const assetsDir = path.join(outputDir, 'assets');
    await fs.ensureDir(assetsDir);
    
    // Extract CSS links
    const cssMatches = html.match(/href=["']([^"']*\.css[^"']*)["']/g) || [];
    console.log(`Found ${cssMatches.length} CSS files`);
    
    // Extract JS scripts
    const jsMatches = html.match(/src=["']([^"']*\.js[^"']*)["']/g) || [];
    console.log(`Found ${jsMatches.length} JS files`);
    
    // Extract images
    const imgMatches = html.match(/src=["']([^"']*\.(png|jpg|jpeg|gif|svg|webp)[^"']*)["']/gi) || [];
    console.log(`Found ${imgMatches.length} images`);
    
    // Download resources
    const allResources = [...cssMatches, ...jsMatches, ...imgMatches];
    
    for (const match of allResources.slice(0, 10)) { // Limit to first 10 to avoid overwhelming
        try {
            const urlMatch = match.match(/(?:href|src)=["']([^"']*)["']/);
            if (urlMatch) {
                let resourceUrl = urlMatch[1];
                
                // Make URL absolute
                if (resourceUrl.startsWith('//')) {
                    resourceUrl = 'https:' + resourceUrl;
                } else if (resourceUrl.startsWith('/')) {
                    resourceUrl = 'https://seha.sa' + resourceUrl;
                } else if (!resourceUrl.startsWith('http')) {
                    resourceUrl = new URL(resourceUrl, baseUrl).href;
                }
                
                console.log(`📥 Downloading: ${resourceUrl}`);
                
                const content = await downloadWithHeaders(resourceUrl, {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                });
                
                if (content && content.length > 0) {
                    const filename = path.basename(new URL(resourceUrl).pathname) || 'resource';
                    await fs.writeFile(path.join(assetsDir, filename), content);
                    console.log(`✅ Saved: ${filename}`);
                }
                
            }
        } catch (error) {
            console.log(`❌ Failed to download resource: ${error.message}`);
        }
    }
}

// Run the retry
retryDownload().catch(console.error);
