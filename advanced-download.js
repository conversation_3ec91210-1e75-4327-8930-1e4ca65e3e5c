const https = require('https');
const http = require('http');
const fs = require('fs-extra');
const path = require('path');
const { URL } = require('url');

async function downloadSehaWebsite() {
    console.log('🚀 Starting advanced download for Seha website...');
    console.log('🎯 Target: https://seha.sa/#/inquiries/slenquiry');
    
    const outputDir = './seha-website-download';
    await fs.ensureDir(outputDir);
    await fs.ensureDir(path.join(outputDir, 'assets'));
    
    // First, let's try to get the main page with a very realistic browser simulation
    try {
        console.log('\n📡 Step 1: Attempting to download main page...');
        
        // Try the main domain first (without the hash route)
        const mainPageHtml = await downloadWithRealBrowserHeaders('https://seha.sa/');
        
        if (mainPageHtml && mainPageHtml.length > 1000 && !mainPageHtml.includes('503 Service unavailable')) {
            console.log('✅ Successfully downloaded main page!');
            console.log(`📄 Content length: ${mainPageHtml.length} characters`);
            
            // Save the main HTML
            await fs.writeFile(path.join(outputDir, 'index.html'), mainPageHtml);
            
            // Extract and download all resources
            await extractAndDownloadResources(mainPageHtml, outputDir);
            
            // Create a modified version that loads the specific route
            const modifiedHtml = createRouteSpecificHtml(mainPageHtml);
            await fs.writeFile(path.join(outputDir, 'inquiries-page.html'), modifiedHtml);
            
            console.log('\n🎉 Download completed successfully!');
            console.log(`📁 Files saved to: ${path.resolve(outputDir)}`);
            
            return true;
        } else {
            console.log('❌ Main page download failed or returned error');
            if (mainPageHtml) {
                console.log(`📄 Response preview: ${mainPageHtml.substring(0, 300)}...`);
            }
        }
        
    } catch (error) {
        console.error('❌ Error during download:', error.message);
    }
    
    console.log('\n🔄 Trying alternative approaches...');
    
    // Try different endpoints
    const alternatives = [
        'https://seha.sa/inquiries',
        'https://seha.sa/en',
        'https://seha.sa/ar',
        'https://www.seha.sa/',
    ];
    
    for (const url of alternatives) {
        try {
            console.log(`\n🔍 Trying: ${url}`);
            const html = await downloadWithRealBrowserHeaders(url);
            
            if (html && html.length > 1000 && !html.includes('503 Service unavailable')) {
                console.log(`✅ Success with ${url}!`);
                await fs.writeFile(path.join(outputDir, `page-${Date.now()}.html`), html);
                await extractAndDownloadResources(html, outputDir);
                return true;
            }
        } catch (error) {
            console.log(`❌ Failed: ${error.message}`);
        }
        
        // Wait between requests
        await sleep(3000);
    }
    
    return false;
}

function downloadWithRealBrowserHeaders(url) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const isHttps = urlObj.protocol === 'https:';
        const client = isHttps ? https : http;
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || (isHttps ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: 'GET',
            headers: {
                'Host': urlObj.hostname,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'ar-SA,ar;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
                'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            },
            timeout: 30000
        };
        
        console.log(`📡 Requesting: ${url}`);
        
        const req = client.request(options, (res) => {
            console.log(`📊 Status: ${res.statusCode}`);
            console.log(`📋 Headers: ${JSON.stringify(res.headers, null, 2)}`);
            
            // Handle redirects
            if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
                console.log(`🔄 Redirecting to: ${res.headers.location}`);
                const redirectUrl = res.headers.location.startsWith('http') 
                    ? res.headers.location 
                    : new URL(res.headers.location, url).href;
                return downloadWithRealBrowserHeaders(redirectUrl).then(resolve).catch(reject);
            }
            
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`📏 Content length: ${data.length}`);
                resolve(data);
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ Request error: ${error.message}`);
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        req.end();
    });
}

async function extractAndDownloadResources(html, outputDir) {
    console.log('\n🔍 Extracting resources from HTML...');
    
    const assetsDir = path.join(outputDir, 'assets');
    
    // Extract all resource URLs
    const resources = new Set();
    
    // CSS files
    const cssMatches = html.match(/href=["']([^"']*\.css[^"']*)["']/g) || [];
    cssMatches.forEach(match => {
        const url = match.match(/href=["']([^"']*)["']/)[1];
        resources.add({ url, type: 'css' });
    });
    
    // JavaScript files
    const jsMatches = html.match(/src=["']([^"']*\.js[^"']*)["']/g) || [];
    jsMatches.forEach(match => {
        const url = match.match(/src=["']([^"']*)["']/)[1];
        resources.add({ url, type: 'js' });
    });
    
    // Images
    const imgMatches = html.match(/src=["']([^"']*\.(png|jpg|jpeg|gif|svg|webp|ico)[^"']*)["']/gi) || [];
    imgMatches.forEach(match => {
        const url = match.match(/src=["']([^"']*)["']/)[1];
        resources.add({ url, type: 'img' });
    });
    
    // Fonts
    const fontMatches = html.match(/url\(["']?([^"')]*\.(woff2?|ttf|eot)[^"')]*)["']?\)/gi) || [];
    fontMatches.forEach(match => {
        const url = match.match(/url\(["']?([^"')]*)["']?\)/)[1];
        resources.add({ url, type: 'font' });
    });
    
    console.log(`📦 Found ${resources.size} resources to download`);
    
    let downloaded = 0;
    for (const resource of Array.from(resources).slice(0, 20)) { // Limit to prevent overwhelming
        try {
            let fullUrl = resource.url;
            
            // Make URL absolute
            if (fullUrl.startsWith('//')) {
                fullUrl = 'https:' + fullUrl;
            } else if (fullUrl.startsWith('/')) {
                fullUrl = 'https://seha.sa' + fullUrl;
            } else if (!fullUrl.startsWith('http')) {
                fullUrl = 'https://seha.sa/' + fullUrl;
            }
            
            console.log(`📥 Downloading ${resource.type}: ${fullUrl}`);
            
            const content = await downloadWithRealBrowserHeaders(fullUrl);
            
            if (content && content.length > 0) {
                const urlObj = new URL(fullUrl);
                let filename = path.basename(urlObj.pathname) || `resource-${Date.now()}`;
                
                // Ensure proper extension
                if (!path.extname(filename)) {
                    filename += `.${resource.type}`;
                }
                
                await fs.writeFile(path.join(assetsDir, filename), content);
                console.log(`✅ Saved: ${filename} (${content.length} bytes)`);
                downloaded++;
            }
            
            await sleep(1000); // Be respectful with requests
            
        } catch (error) {
            console.log(`❌ Failed to download ${resource.url}: ${error.message}`);
        }
    }
    
    console.log(`✅ Successfully downloaded ${downloaded} resources`);
}

function createRouteSpecificHtml(originalHtml) {
    // Modify the HTML to automatically navigate to the inquiries route
    const modifiedHtml = originalHtml.replace(
        '</head>',
        `
        <script>
            // Auto-navigate to inquiries page when loaded
            window.addEventListener('load', function() {
                if (window.location.hash !== '#/inquiries/slenquiry') {
                    window.location.hash = '#/inquiries/slenquiry';
                }
            });
        </script>
        </head>`
    );
    
    return modifiedHtml;
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the download
downloadSehaWebsite()
    .then(success => {
        if (success) {
            console.log('\n🎉 Download completed successfully!');
        } else {
            console.log('\n❌ All download attempts failed.');
            console.log('💡 Try the manual browser method:');
            console.log('   1. Open https://seha.sa/#/inquiries/slenquiry in your browser');
            console.log('   2. Right-click → Save as → Webpage, Complete');
        }
    })
    .catch(console.error);
