#!/usr/bin/env python
"""
Script to create database tables and initial data
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'warehouse_system.settings')
django.setup()

from django.core.management import execute_from_command_line

def create_database():
    """Create database tables"""
    print("Creating database migrations...")
    execute_from_command_line(['manage.py', 'makemigrations'])
    
    print("Applying migrations...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    print("Database created successfully!")

if __name__ == '__main__':
    create_database()
