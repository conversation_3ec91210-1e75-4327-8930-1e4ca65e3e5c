<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <script
      src="https://objectstorage.me-jeddah-1.oraclecloud.com/n/axcv6afvufi6/b/Rum-JS-Public/o/elastic-apm-rum.umd.min.js"
      crossorigin
    ></script>
    <script>
      elasticApm.init({
        serviceName: 'Seha2-Frontend',
        serverUrl: 'https://apm-lean.acuative-me.com:8200/',
        environment: 'Production',
      })
    </script>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <link rel="apple-touch-icon" href="/logo.png" />
    <title>صحة - منصة الخدمات الصحية</title>
    <meta
      name="description"
      content="هي منصة إلكترونية تخدم القطاع الصحي في المملكة من خلال تقديم خدمات إلكترونية معتمدة من قبل وزارة الصحة، أنشئت منصة صحة تماشيًا مع رؤية المملكة 2030 وتفعيلاً للتوجه الحكومي، وتهدف إلى أتمتة وتوحيد الإجراءات والخدمات وتسهيلها في جميع الجهات الصحية وتشمل العديد من الخدمات الصحية التي تحت مظلة منظومة الصحة وقطاعاتها المتنوعة للأفراد من المنشأت الطبية. "
    />
    <!--
      seha-favicon-manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    --> 
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap"
      rel="stylesheet"
    />
    <script type="module" crossorigin src="/assets/index-lHQIrFQb.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/components-Au9m-AvA.js">
    <link rel="modulepreload" crossorigin href="/assets/main-BkLylkcu.js">
    <link rel="stylesheet" crossorigin href="/assets/index-1OvumEaI.css">
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
