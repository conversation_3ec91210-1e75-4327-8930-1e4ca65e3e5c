const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

async function downloadCompleteWebsite() {
    console.log('🎉 SUCCESS! We got the main HTML file!');
    console.log('🚀 Now downloading all referenced assets...');
    
    const outputDir = './seha-complete-download';
    await fs.ensureDir(outputDir);
    await fs.ensureDir(path.join(outputDir, 'assets'));
    
    // Copy the main HTML file
    await fs.copy('seha-powershell.html', path.join(outputDir, 'index.html'));
    console.log('✅ Copied main HTML file');
    
    // Read the HTML to extract asset URLs
    const html = await fs.readFile('seha-powershell.html', 'utf8');
    
    // Extract all asset URLs from the HTML
    const assets = [
        // JavaScript files
        'https://objectstorage.me-jeddah-1.oraclecloud.com/n/axcv6afvufi6/b/Rum-JS-Public/o/elastic-apm-rum.umd.min.js',
        'https://seha.sa/assets/index-lHQIrFQb.js',
        'https://seha.sa/assets/components-Au9m-AvA.js',
        'https://seha.sa/assets/main-BkLylkcu.js',
        
        // CSS files
        'https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap',
        'https://seha.sa/assets/index-1OvumEaI.css',
        
        // Icons and images
        'https://seha.sa/favicon.png',
        'https://seha.sa/logo.png',
        'https://seha.sa/manifest.json'
    ];
    
    console.log(`📦 Found ${assets.length} assets to download`);
    
    let downloaded = 0;
    let failed = 0;
    
    for (const assetUrl of assets) {
        try {
            console.log(`📥 Downloading: ${assetUrl}`);
            
            const filename = getFilenameFromUrl(assetUrl);
            const outputPath = path.join(outputDir, 'assets', filename);
            
            const success = await downloadWithPowerShell(assetUrl, outputPath);
            
            if (success) {
                console.log(`✅ Downloaded: ${filename}`);
                downloaded++;
            } else {
                console.log(`❌ Failed: ${filename}`);
                failed++;
            }
            
            // Small delay between downloads
            await sleep(500);
            
        } catch (error) {
            console.log(`❌ Error downloading ${assetUrl}: ${error.message}`);
            failed++;
        }
    }
    
    // Now let's try to get additional assets that might be referenced in the CSS/JS files
    console.log('\n🔍 Looking for additional assets in downloaded files...');
    
    try {
        const cssFile = path.join(outputDir, 'assets', 'index-1OvumEaI.css');
        if (await fs.pathExists(cssFile)) {
            const cssContent = await fs.readFile(cssFile, 'utf8');
            const additionalAssets = extractAssetsFromCSS(cssContent);
            
            for (const asset of additionalAssets.slice(0, 10)) { // Limit to prevent overwhelming
                try {
                    const filename = getFilenameFromUrl(asset);
                    const outputPath = path.join(outputDir, 'assets', filename);
                    
                    console.log(`📥 Additional asset: ${asset}`);
                    const success = await downloadWithPowerShell(asset, outputPath);
                    
                    if (success) {
                        console.log(`✅ Downloaded additional: ${filename}`);
                        downloaded++;
                    }
                    
                    await sleep(500);
                } catch (error) {
                    console.log(`❌ Failed additional asset: ${error.message}`);
                }
            }
        }
    } catch (error) {
        console.log(`⚠️ Could not process CSS for additional assets: ${error.message}`);
    }
    
    // Create a modified HTML file that works offline
    await createOfflineVersion(outputDir);
    
    console.log('\n🎉 Download Summary:');
    console.log(`✅ Successfully downloaded: ${downloaded} files`);
    console.log(`❌ Failed downloads: ${failed} files`);
    console.log(`📁 All files saved to: ${path.resolve(outputDir)}`);
    console.log('\n📋 Files created:');
    console.log('   - index.html (original)');
    console.log('   - index-offline.html (modified for offline use)');
    console.log('   - assets/ (all downloaded resources)');
    
    return { downloaded, failed };
}

function downloadWithPowerShell(url, outputPath) {
    return new Promise((resolve) => {
        const command = `Invoke-WebRequest -Uri '${url}' -UserAgent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36' -OutFile '${outputPath}'`;
        
        const process = spawn('powershell', ['-Command', command], {
            stdio: 'pipe'
        });
        
        let output = '';
        let error = '';
        
        process.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        process.stderr.on('data', (data) => {
            error += data.toString();
        });
        
        process.on('close', (code) => {
            if (code === 0) {
                resolve(true);
            } else {
                console.log(`PowerShell error for ${url}: ${error}`);
                resolve(false);
            }
        });
        
        // Timeout after 30 seconds
        setTimeout(() => {
            process.kill();
            resolve(false);
        }, 30000);
    });
}

function getFilenameFromUrl(url) {
    try {
        const urlObj = new URL(url);
        let filename = path.basename(urlObj.pathname);
        
        if (!filename || filename === '/') {
            // Generate filename from URL
            filename = urlObj.hostname.replace(/\./g, '-') + '-' + Date.now();
        }
        
        // Ensure proper extension
        if (!path.extname(filename)) {
            if (url.includes('.css')) filename += '.css';
            else if (url.includes('.js')) filename += '.js';
            else if (url.includes('.png')) filename += '.png';
            else if (url.includes('.json')) filename += '.json';
            else filename += '.txt';
        }
        
        return filename;
    } catch (error) {
        return `asset-${Date.now()}.txt`;
    }
}

function extractAssetsFromCSS(cssContent) {
    const assets = [];
    
    // Extract URLs from CSS
    const urlMatches = cssContent.match(/url\(['"]?([^'")]+)['"]?\)/g) || [];
    
    urlMatches.forEach(match => {
        const url = match.match(/url\(['"]?([^'")]+)['"]?\)/)[1];
        
        if (url.startsWith('http')) {
            assets.push(url);
        } else if (url.startsWith('/')) {
            assets.push('https://seha.sa' + url);
        }
    });
    
    return assets;
}

async function createOfflineVersion(outputDir) {
    console.log('\n🔧 Creating offline version...');
    
    const htmlPath = path.join(outputDir, 'index.html');
    let html = await fs.readFile(htmlPath, 'utf8');
    
    // Replace external URLs with local paths
    html = html.replace(/https:\/\/seha\.sa\/assets\//g, './assets/');
    html = html.replace(/https:\/\/objectstorage\.me-jeddah-1\.oraclecloud\.com\/n\/axcv6afvufi6\/b\/Rum-JS-Public\/o\//g, './assets/');
    html = html.replace(/https:\/\/fonts\.googleapis\.com\/css2\?family=Cairo:wght@400;600;700;900&display=swap/g, './assets/css2');
    
    // Add a note about offline version
    html = html.replace('<title>', '<title>[OFFLINE] ');
    
    await fs.writeFile(path.join(outputDir, 'index-offline.html'), html);
    console.log('✅ Created offline version: index-offline.html');
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the complete download
downloadCompleteWebsite()
    .then(result => {
        console.log('\n🎊 DOWNLOAD COMPLETED!');
        console.log('🌐 You now have the complete Seha website design!');
    })
    .catch(console.error);
